/**
 * 每日行程页面
 * 显示具体某一天的行程安排详情
 */

import { Trip, DailyItinerary, Activity, ActivityType, TripDataManager } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
  selectedDate?: string;
}

@Entry
@Component
struct DailyItineraryPage {
  @State trip: Trip | null = null;
  @State itineraries: DailyItinerary[] = [];
  @State selectedDate: string = '';
  @State selectedItinerary: DailyItinerary | null = null;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('DailyItineraryPage: 页面即将显示');
    const params = router.getParams();
    console.log('DailyItineraryPage: 获取到的参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined) {
        const tripId = routeParams.tripId;
        console.log(`DailyItineraryPage: 获取行程ID: ${tripId}`);
        
        // 获取行程信息
        const foundTrip = this.tripManager.getTripById(tripId);
        this.trip = foundTrip || null;

        if (this.trip) {
          console.log(`DailyItineraryPage: 找到行程: ${this.trip.title}`);
          this.itineraries = this.tripManager.getTripDetails(this.trip.id);
          
          // 设置默认选中的日期
          if (routeParams.selectedDate) {
            this.selectedDate = routeParams.selectedDate;
          } else if (this.itineraries.length > 0) {
            this.selectedDate = this.itineraries[0].date;
          }
          
          // 设置选中的行程
          this.updateSelectedItinerary();
          
          console.log(`DailyItineraryPage: 加载了 ${this.itineraries.length} 天行程`);
          console.log(`DailyItineraryPage: 选中日期: ${this.selectedDate}`);
        } else {
          console.error(`DailyItineraryPage: 未找到ID为 ${tripId} 的行程`);
        }
      }
    } else {
      console.error('DailyItineraryPage: 未获取到有效的路由参数');
    }
  }

  // 更新选中的行程
  updateSelectedItinerary() {
    this.selectedItinerary = this.itineraries.find(itinerary => itinerary.date === this.selectedDate) || null;
    console.log(`DailyItineraryPage: 更新选中行程:`, this.selectedItinerary?.title);
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理日期选择
  handleDateSelect = (date: string) => {
    console.log(`DailyItineraryPage: 选择日期: ${date}`);
    this.selectedDate = date;
    this.updateSelectedItinerary();
  }

  // 处理活动点击
  handleActivityClick = (activity: Activity) => {
    console.log(`DailyItineraryPage: 点击活动: ${activity.title}`);
    // 这里可以跳转到活动详情页面或进行其他操作
  }

  // 处理添加活动
  handleAddActivity = () => {
    console.log('DailyItineraryPage: 点击添加活动');
    // 这里可以跳转到添加活动页面
  }

  // 获取活动类型图标
  getActivityIcon(type: ActivityType): string {
    switch (type) {
      case ActivityType.SIGHTSEEING:
        return '🏛️';
      case ActivityType.DINING:
        return '🍽️';
      case ActivityType.SHOPPING:
        return '🛍️';
      case ActivityType.TRANSPORTATION:
        return '🚗';
      case ActivityType.ACCOMMODATION:
        return '🏨';
      case ActivityType.ENTERTAINMENT:
        return '🎭';
      default:
        return '📍';
    }
  }

  // 格式化日期显示
  formatDateDisplay(date: string): string {
    const dateObj = new Date(date);
    const month = dateObj.getMonth() + 1;
    const day = dateObj.getDate();
    return `${month}/${day}`;
  }

  // 格式化时间显示
  formatTime(time: string): string {
    return time;
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.ARROW_LEFT,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Normal)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleBack)

        Text(this.trip ? `${this.trip.title} - 每日行程` : '每日行程')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位符，保持标题居中
        Row()
          .width(40)
          .height(40)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(THEME_COLORS.background)

      if (this.trip && this.itineraries.length > 0) {
        Column() {
          // 日期选择器
          Row() {
            Text('第1天')
              .fontSize(14)
              .fontColor(THEME_COLORS.primary)
              .backgroundColor('#E8F5E8')
              .padding({ left: 12, right: 12, top: 6, bottom: 6 })
              .borderRadius(16)

            Text('第2天')
              .fontSize(14)
              .fontColor(THEME_COLORS.textSecondary)
              .padding({ left: 12, right: 12, top: 6, bottom: 6 })
              .borderRadius(16)
              .margin({ left: 8 })
          }
          .width('100%')
          .padding({ left: 16, right: 16, top: 16, bottom: 16 })
          .justifyContent(FlexAlign.Start)

          // 行程内容
          if (this.selectedItinerary) {
            Scroll() {
              Column() {
                // 抵达与探索标题
                Row() {
                  Text(this.selectedItinerary.title)
                    .fontSize(16)
                    .fontWeight(600)
                    .fontColor(THEME_COLORS.textPrimary)
                    .layoutWeight(1)

                  Text(`${this.formatDateDisplay(this.selectedItinerary.date)} 周一 • ${this.selectedItinerary.activities.length}个活动`)
                    .fontSize(14)
                    .fontColor(THEME_COLORS.textSecondary)
                }
                .width('100%')
                .padding({ left: 16, right: 16, bottom: 16 })
                .alignItems(VerticalAlign.Center)

                // 活动列表
                List({ space: 0 }) {
                  ForEach(this.selectedItinerary.activities, (activity: Activity, index: number) => {
                    ListItem() {
                      this.buildActivityItem(activity, index)
                    }
                  })
                }
                .width('100%')
                .layoutWeight(1)
              }
            }
            .layoutWeight(1)
          } else {
            // 空状态
            Column() {
              Text('暂无行程安排')
                .fontSize(16)
                .fontColor(THEME_COLORS.textSecondary)
                .margin({ top: 60 })
            }
            .width('100%')
            .layoutWeight(1)
            .justifyContent(FlexAlign.Center)
          }

          // 底部添加活动按钮
          Button('+ 添加活动')
            .fontSize(16)
            .fontColor(Color.White)
            .backgroundColor(THEME_COLORS.primary)
            .width('calc(100% - 32vp)')
            .height(48)
            .borderRadius(24)
            .margin({ left: 16, right: 16, bottom: 16 })
            .onClick(this.handleAddActivity)
        }
        .layoutWeight(1)
        .backgroundColor(THEME_COLORS.background)
      } else {
        // 加载状态或错误状态
        Column() {
          Text('加载中...')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .backgroundColor(THEME_COLORS.background)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }

  // 构建活动项组件
  @Builder
  buildActivityItem(activity: Activity, index: number) {
    Row() {
      // 左侧时间线
      Column() {
        // 圆点
        Stack() {
          Circle({ width: 12, height: 12 })
            .fill(activity.type === ActivityType.TRANSPORTATION ? THEME_COLORS.primary : '#E0E0E0')
          
          if (activity.type === ActivityType.TRANSPORTATION) {
            Text(this.getActivityIcon(activity.type))
              .fontSize(8)
          }
        }
        .width(12)
        .height(12)
        .margin({ top: 4 })

        // 连接线（除了最后一个）
        if (index < (this.selectedItinerary?.activities.length || 0) - 1) {
          Line()
            .width(1)
            .height(60)
            .stroke('#E0E0E0')
            .strokeWidth(1)
            .margin({ top: 4 })
        }
      }
      .width(20)
      .alignItems(HorizontalAlign.Center)

      // 右侧内容
      Column() {
        Row() {
          // 活动图标
          Text(this.getActivityIcon(activity.type))
            .fontSize(20)
            .margin({ right: 12 })

          Column() {
            Text(activity.title)
              .fontSize(16)
              .fontWeight(600)
              .fontColor(THEME_COLORS.textPrimary)
              .alignSelf(ItemAlign.Start)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })

            if (activity.description) {
              Text(activity.description)
                .fontSize(14)
                .fontColor(THEME_COLORS.textSecondary)
                .alignSelf(ItemAlign.Start)
                .margin({ top: 2 })
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
            }

            if (activity.location) {
              Text(activity.location)
                .fontSize(12)
                .fontColor(THEME_COLORS.textTertiary)
                .alignSelf(ItemAlign.Start)
                .margin({ top: 2 })
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })
            }
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)

          Column() {
            Text(this.formatTime(activity.startTime))
              .fontSize(14)
              .fontWeight(600)
              .fontColor(THEME_COLORS.textPrimary)
              .textAlign(TextAlign.End)

            Text(`${activity.endTime.split(':')[0]}分钟`)
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)
              .textAlign(TextAlign.End)
              .margin({ top: 2 })
          }
          .alignItems(HorizontalAlign.End)

          Button('...')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
            .backgroundColor(Color.Transparent)
            .width(32)
            .height(32)
            .margin({ left: 8 })
            .onClick(() => {
              console.log(`点击活动菜单: ${activity.title}`);
            })
        }
        .width('100%')
        .alignItems(VerticalAlign.Top)
      }
      .layoutWeight(1)
      .margin({ left: 16 })
      .padding({ top: 4, bottom: 16 })
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
    .alignItems(VerticalAlign.Top)
    .onClick(() => this.handleActivityClick(activity))
  }
}
